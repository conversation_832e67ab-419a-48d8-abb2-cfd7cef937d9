"use client"

import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"

interface Tab {
  id: string
  label: string
  count?: number
}

interface NavigationTabsProps {
  tabs: Tab[]
  activeTab: string
  onTabChange: (tabId: string) => void
  className?: string
}

export function NavigationTabs({ tabs, activeTab, onTabChange, className }: NavigationTabsProps) {
  return (
    <div className={cn("border-b border-gray-200 dark:border-gray-700", className)}>
      <nav className="-mb-px flex space-x-8">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => onTabChange(tab.id)}
            className={cn(
              "border-b-2 py-2 px-1 text-sm font-medium transition-colors flex items-center gap-2",
              activeTab === tab.id
                ? "border-orange-500 text-orange-600"
                : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300"
            )}
          >
            {tab.label}
            {tab.count !== undefined && (
              <Badge
                variant={activeTab === tab.id ? "default" : "secondary"}
                className={cn(
                  "text-xs",
                  activeTab === tab.id
                    ? "bg-orange-100 text-orange-600 hover:bg-orange-100"
                    : ""
                )}
              >
                {tab.count}
              </Badge>
            )}
          </button>
        ))}
      </nav>
    </div>
  )
}
