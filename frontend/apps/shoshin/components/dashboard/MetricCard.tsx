"use client"

import { Card, CardContent, CardDescription, CardHeader } from "@/components/ui/card"
import { <PERSON><PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { cn } from "@/lib/utils"

interface MetricCardProps {
  title: string
  timeRange: string
  value: string | number
  unit?: string
  deviation?: number
  deviationUnit?: string
  className?: string
  isActive?: boolean
  onClick?: () => void
}

export function MetricCard({
  title,
  timeRange,
  value,
  unit,
  deviation,
  deviationUnit,
  className,
  isActive = false,
  onClick
}: MetricCardProps) {
  const getDeviationColor = (deviation: number) => {
    if (deviation > 0) return "text-green-600"
    if (deviation < 0) return "text-red-600"
    return "text-gray-500"
  }

  const getDeviationIcon = (deviation: number) => {
    if (deviation > 0) return "↗"
    if (deviation < 0) return "↘"
    return "→"
  }

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Card
            className={cn(
              "cursor-pointer transition-all duration-200 hover:shadow-md",
              isActive && "ring-2 ring-orange-500 border-orange-500",
              className
            )}
            onClick={onClick}
          >
            <CardHeader className="pb-2 space-y-1">
              <CardDescription className="text-xs font-medium text-gray-600 dark:text-gray-400">
                {title}
              </CardDescription>
              <CardDescription className="text-xs text-gray-400">
                {timeRange}
              </CardDescription>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="flex items-baseline space-x-2">
                <div className="text-2xl font-bold text-gray-900 dark:text-white">
                  {value}
                  {unit && <span className="text-sm font-normal text-gray-500 ml-1">{unit}</span>}
                </div>
                {deviation !== undefined && deviation !== null && (
                  <div className={cn("flex items-center text-xs", getDeviationColor(deviation))}>
                    <span className="mr-1">{getDeviationIcon(deviation)}</span>
                    <span>
                      {Math.abs(deviation)}{deviationUnit}
                    </span>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TooltipTrigger>
        <TooltipContent>
          <p>Click to filter by {title.toLowerCase()}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  )
}

interface MetricsGridProps {
  className?: string
  activeMetric?: string
  onMetricClick?: (metric: string) => void
}

export function MetricsGrid({ className, activeMetric, onMetricClick }: MetricsGridProps) {
  const metrics = [
    {
      id: "total",
      title: "Prod. executions",
      timeRange: "Last 7 days",
      value: 0,
      deviation: null
    },
    {
      id: "failed",
      title: "Failed prod. executions", 
      timeRange: "Last 7 days",
      value: 0,
      deviation: null
    },
    {
      id: "failureRate",
      title: "Failure rate",
      timeRange: "Last 7 days", 
      value: 0,
      unit: "%",
      deviation: null
    },
    {
      id: "timeSaved",
      title: "Time saved",
      timeRange: "Last 7 days",
      value: 0,
      deviation: null
    },
    {
      id: "runtime",
      title: "Runtime (avg.)",
      timeRange: "Last 7 days",
      value: 0,
      unit: "s",
      deviation: null
    }
  ]

  return (
    <div className={cn("grid grid-cols-1 md:grid-cols-5 gap-4", className)}>
      {metrics.map((metric) => (
        <MetricCard
          key={metric.id}
          title={metric.title}
          timeRange={metric.timeRange}
          value={metric.value}
          unit={metric.unit}
          deviation={metric.deviation}
          isActive={activeMetric === metric.id}
          onClick={() => onMetricClick?.(metric.id)}
        />
      ))}
    </div>
  )
}
