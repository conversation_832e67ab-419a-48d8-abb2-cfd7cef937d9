"use client"

import { Card, CardContent } from "@/components/ui/card"
import { cn } from "@/lib/utils"

interface WelcomeSectionProps {
  userName?: string
  className?: string
  onCreateWorkflow?: () => void
}

export function WelcomeSection({ userName = "User", className, onCreateWorkflow }: WelcomeSectionProps) {
  return (
    <div className={cn("text-center py-16", className)}>
      <div className="mb-4">
        <span className="text-4xl">👋</span>
      </div>
      <h2 className="text-xl font-semibold mb-2 text-gray-900 dark:text-white">
        Welcome {userName}!
      </h2>
      <p className="text-gray-600 dark:text-gray-400 mb-8">
        Create your first workflow
      </p>
      
      {/* Start from scratch card */}
      <div className="max-w-sm mx-auto">
        <Card 
          className="bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 cursor-pointer transition-colors border-2 border-dashed border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500"
          onClick={onCreateWorkflow}
        >
          <CardContent className="p-8">
            <div className="mb-4">
              <div className="w-12 h-12 bg-gray-300 dark:bg-gray-600 rounded mx-auto flex items-center justify-center">
                <svg 
                  className="w-6 h-6 text-gray-600 dark:text-gray-300" 
                  fill="none" 
                  stroke="currentColor" 
                  viewBox="0 0 24 24"
                >
                  <path 
                    strokeLinecap="round" 
                    strokeLinejoin="round" 
                    strokeWidth={2} 
                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" 
                  />
                </svg>
              </div>
            </div>
            <div className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Start from scratch
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
