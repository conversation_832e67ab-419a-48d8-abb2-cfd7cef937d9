{"name": "@repo/shoshin", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "check-types": "tsc --noEmit"}, "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-brands-svg-icons": "^6.7.2", "@fortawesome/free-regular-svg-icons": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-portal": "^1.1.9", "@radix-ui/react-slot": "^1.2.3", "@tabler/icons-react": "^3.34.0", "@xyflow/react": "^12.8.1", "framer-motion": "^12.23.0", "lucide-react": "^0.525.0", "marked": "^16.0.0", "mini-svg-data-uri": "^1.4.4", "next": "15.3.5", "react": "^19.0.0", "react-dom": "^19.0.0", "react-markdown": "^10.1.0", "remark-gfm": "^4.0.1", "shiki": "^3.7.0", "simple-icons": "^15.5.0", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/eslintrc": "^3", "@iconify/json": "^2.2.356", "@tailwindcss/typography": "^0.5.16", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "eslint": "^9", "eslint-config-next": "15.3.5", "postcss": "^8.5.6", "tailwind-merge": "^3.3.1", "tailwindcss": "^3", "tailwindcss-animate": "^1.0.7", "typescript": "^5", "unplugin-icons": "^22.1.0"}}